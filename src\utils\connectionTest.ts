// Connection test utility to diagnose network and database issues

import { logger } from '@/lib/logger'

export interface ConnectionTestResult {
  success: boolean
  latency: number
  error?: string
  details?: unknown
}

export async function testSupabaseConnection(): Promise<ConnectionTestResult> {
  const startTime = Date.now()
  
  try {
    const response = await fetch('/api/products?limit=1', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      signal: AbortSignal.timeout(10000) // 10 second timeout
    })

    const latency = Date.now() - startTime

    if (!response.ok) {
      return {
        success: false,
        latency,
        error: `HTTP ${response.status}: ${response.statusText}`,
        details: {
          status: response.status,
          statusText: response.statusText
        }
      }
    }

    const data = await response.json()
    
    return {
      success: true,
      latency,
      details: {
        dataReceived: !!data,
        hasProducts: Array.isArray(data.products) || Array.isArray(data.data?.products)
      }
    }
  } catch (error) {
    const latency = Date.now() - startTime
    
    return {
      success: false,
      latency,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: {
        errorType: error instanceof Error ? error.name : 'Unknown',
        timeout: latency >= 10000
      }
    }
  }
}

export async function testNetworkConnection(): Promise<ConnectionTestResult> {
  const startTime = Date.now()
  
  try {
    const response = await fetch('https://httpbin.org/get', {
      method: 'GET',
      signal: AbortSignal.timeout(5000) // 5 second timeout
    })

    const latency = Date.now() - startTime

    return {
      success: response.ok,
      latency,
      error: response.ok ? '' : `HTTP ${response.status}`,
      details: {
        status: response.status
      }
    }
  } catch (error) {
    const latency = Date.now() - startTime
    
    return {
      success: false,
      latency,
      error: error instanceof Error ? error.message : 'Network error',
      details: {
        errorType: error instanceof Error ? error.name : 'Unknown',
        timeout: latency >= 5000
      }
    }
  }
}

export async function runConnectionDiagnostics() {
  logger.info('Running connection diagnostics')

  const networkTest = await testNetworkConnection()
  logger.info('Network test completed', { networkTest })

  const supabaseTest = await testSupabaseConnection()
  logger.info('Supabase test completed', { supabaseTest })
  
  return {
    network: networkTest,
    supabase: supabaseTest,
    recommendations: generateRecommendations(networkTest, supabaseTest)
  }
}

function generateRecommendations(networkTest: ConnectionTestResult, supabaseTest: ConnectionTestResult): string[] {
  const recommendations: string[] = []
  
  if (!networkTest.success) {
    recommendations.push('Check your internet connection')
    recommendations.push('Try disabling VPN or proxy if enabled')
  } else if (networkTest.latency > 3000) {
    recommendations.push('Your internet connection is slow - consider using a faster connection')
  }
  
  if (!supabaseTest.success) {
    if (supabaseTest.error?.includes('timeout') || supabaseTest.details?.timeout) {
      recommendations.push('Database queries are timing out - this may be a Supabase performance issue')
      recommendations.push('Try again in a few minutes')
    } else if (supabaseTest.error?.includes('HTTP 5')) {
      recommendations.push('Supabase server error - check Supabase status page')
    } else if (supabaseTest.error?.includes('HTTP 4')) {
      recommendations.push('Check your Supabase configuration in .env.local')
    }
  } else if (supabaseTest.latency > 5000) {
    recommendations.push('Database queries are slow - consider optimizing your queries')
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Connection looks good! The issue may be temporary.')
  }
  
  return recommendations
}
